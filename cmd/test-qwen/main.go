package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go-api-solve/internal/config"
)

// QwenMessage 正确的Qwen消息格式
type QwenMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"`
}

// QwenContent 多模态内容格式
type QwenContent struct {
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

// QwenRequest 正确的请求格式
type QwenRequest struct {
	Model      string        `json:"model"`
	Input      QwenInput     `json:"input"`
	Parameters QwenParams    `json:"parameters"`
}

type QwenInput struct {
	Messages []QwenMessage `json:"messages"`
}

type QwenParams struct {
	Temperature       float64 `json:"temperature"`
	TopP              float64 `json:"top_p"`
	TopK              int     `json:"top_k"`
	RepetitionPenalty float64 `json:"repetition_penalty"`
	PresencePenalty   float64 `json:"presence_penalty"`
	ResponseFormat    string  `json:"response_format"`
}

func main() {
	cfg := config.LoadConfig()
	
	// 测试不同的格式
	fmt.Println("Testing different Qwen API formats...")
	
	// 格式1：Image字段直接在Message中
	fmt.Println("\n=== Format 1: Image field in message ===")
	testFormat1(cfg)
	
	// 格式2：Content为数组格式
	fmt.Println("\n=== Format 2: Content as array ===")
	testFormat2(cfg)
	
	// 格式3：Content为字符串，图片URL在其中
	fmt.Println("\n=== Format 3: Content as string with image URL ===")
	testFormat3(cfg)
}

func testFormat1(cfg *config.Config) {
	// 当前使用的格式
	type MessageWithImage struct {
		Role    string `json:"role"`
		Content string `json:"content"`
		Image   string `json:"image,omitempty"`
	}
	
	requestBody := struct {
		Model      string             `json:"model"`
		Input      struct {
			Messages []MessageWithImage `json:"messages"`
		} `json:"input"`
		Parameters QwenParams `json:"parameters"`
	}{
		Model: "qwen-vl-plus",
		Input: struct {
			Messages []MessageWithImage `json:"messages"`
		}{
			Messages: []MessageWithImage{
				{
					Role:    "system",
					Content: "你是一个专业的图片题目识别助手",
				},
				{
					Role:    "user",
					Content: "请识别这张图片中的题目内容",
					Image:   "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg",
				},
			},
		},
		Parameters: QwenParams{
			Temperature:       0.0,
			TopP:              0.8,
			TopK:              50,
			RepetitionPenalty: 1.05,
			PresencePenalty:   1.5,
			ResponseFormat:    "json_object",
		},
	}
	
	testRequest(requestBody, cfg.AI.QwenKey)
}

func testFormat2(cfg *config.Config) {
	// Content为数组格式
	requestBody := QwenRequest{
		Model: "qwen-vl-plus",
		Input: QwenInput{
			Messages: []QwenMessage{
				{
					Role:    "system",
					Content: "你是一个专业的图片题目识别助手",
				},
				{
					Role: "user",
					Content: []QwenContent{
						{Text: "请识别这张图片中的题目内容"},
						{Image: "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"},
					},
				},
			},
		},
		Parameters: QwenParams{
			Temperature:       0.0,
			TopP:              0.8,
			TopK:              50,
			RepetitionPenalty: 1.05,
			PresencePenalty:   1.5,
			ResponseFormat:    "json_object",
		},
	}
	
	testRequest(requestBody, cfg.AI.QwenKey)
}

func testFormat3(cfg *config.Config) {
	// Content为字符串，包含图片URL
	requestBody := QwenRequest{
		Model: "qwen-vl-plus",
		Input: QwenInput{
			Messages: []QwenMessage{
				{
					Role:    "system",
					Content: "你是一个专业的图片题目识别助手",
				},
				{
					Role:    "user",
					Content: "请识别这张图片中的题目内容：<img>https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg</img>",
				},
			},
		},
		Parameters: QwenParams{
			Temperature:       0.0,
			TopP:              0.8,
			TopK:              50,
			RepetitionPenalty: 1.05,
			PresencePenalty:   1.5,
			ResponseFormat:    "json_object",
		},
	}
	
	testRequest(requestBody, cfg.AI.QwenKey)
}

func testRequest(requestBody interface{}, apiKey string) {
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("❌ Marshal error: %v\n", err)
		return
	}
	
	fmt.Printf("Request body: %s\n", string(jsonData))
	
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	req, err := http.NewRequestWithContext(ctx, "POST", 
		"https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation", 
		bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Request creation error: %v\n", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Request error: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Read response error: %v\n", err)
		return
	}
	
	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(bodyBytes))
	
	if resp.StatusCode == 200 {
		fmt.Println("✅ Success!")
	} else {
		fmt.Println("❌ Failed!")
	}
}
