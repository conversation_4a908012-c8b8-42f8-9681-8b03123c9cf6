package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go-api-solve/internal/config"
)

func main() {
	cfg := config.LoadConfig()
	
	fmt.Println("🔍 调试Qwen API返回内容...")
	
	// 使用官方文档中的正确格式
	requestBody := map[string]interface{}{
		"model": "qwen-vl-plus",
		"messages": []map[string]interface{}{
			{
				"role": "user",
				"content": []map[string]interface{}{
					{
						"type": "text",
						"text": "请识别这张图片中的题目内容，并以JSON格式返回题目信息。返回格式必须包含：question_type（题目类型：单选题/多选题/判断题），question_text（题目内容），以及对应的选项（A、B、C、D或Y、N）。",
					},
					{
						"type": "image_url",
						"image_url": map[string]string{
							"url": "http://solve.igmdns.com/img/24.jpg",
						},
					},
				},
			},
		},
		"temperature": 0.0,
		"top_p":       0.8,
		"response_format": map[string]string{"type": "json_object"},
	}
	
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("❌ Marshal error: %v\n", err)
		return
	}
	
	fmt.Printf("📤 Request body: %s\n\n", string(jsonData))
	
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	
	req, err := http.NewRequestWithContext(ctx, "POST", 
		"https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", 
		bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Request creation error: %v\n", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+cfg.AI.QwenKey)
	req.Header.Set("Content-Type", "application/json")
	
	fmt.Println("📡 Sending request to Qwen API...")
	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Request error: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Read response error: %v\n", err)
		return
	}
	
	fmt.Printf("📥 Status: %d\n", resp.StatusCode)
	fmt.Printf("📥 Raw Response: %s\n\n", string(bodyBytes))
	
	if resp.StatusCode == 200 {
		fmt.Println("✅ API调用成功!")
		
		// 解析响应
		var response map[string]interface{}
		if err := json.Unmarshal(bodyBytes, &response); err == nil {
			if choices, ok := response["choices"].([]interface{}); ok && len(choices) > 0 {
				if choice, ok := choices[0].(map[string]interface{}); ok {
					if message, ok := choice["message"].(map[string]interface{}); ok {
						if content, ok := message["content"].(string); ok {
							fmt.Printf("🎯 AI返回的内容:\n%s\n\n", content)
							
							// 尝试解析AI返回的JSON内容
							fmt.Println("🔍 尝试解析AI返回的JSON内容...")
							var aiContent map[string]interface{}
							if err := json.Unmarshal([]byte(content), &aiContent); err != nil {
								fmt.Printf("❌ AI返回的内容不是有效的JSON: %v\n", err)
								fmt.Printf("📝 原始内容: %s\n", content)
							} else {
								fmt.Println("✅ AI返回的JSON解析成功:")
								prettyJSON, _ := json.MarshalIndent(aiContent, "", "  ")
								fmt.Printf("%s\n\n", string(prettyJSON))
								
								// 检查question_type字段
								if questionType, ok := aiContent["question_type"].(string); ok {
									fmt.Printf("📋 题目类型: %s\n", questionType)
									
									// 验证题目类型
									validTypes := []string{"判断题", "多选题", "单选题"}
									isValid := false
									for _, validType := range validTypes {
										if questionType == validType {
											isValid = true
											break
										}
									}
									
									if isValid {
										fmt.Printf("✅ 题目类型有效\n")
									} else {
										fmt.Printf("❌ 题目类型无效! 期望: %v, 实际: %s\n", validTypes, questionType)
									}
								} else {
									fmt.Printf("❌ 缺少question_type字段或类型不正确\n")
								}
								
								// 检查question_text字段
								if questionText, ok := aiContent["question_text"].(string); ok {
									fmt.Printf("📝 题目内容: %s\n", questionText)
								} else {
									fmt.Printf("❌ 缺少question_text字段或类型不正确\n")
								}
							}
						}
					}
				}
			}
		}
	} else {
		fmt.Printf("❌ API调用失败! Status: %d\n", resp.StatusCode)
		fmt.Printf("📄 错误响应: %s\n", string(bodyBytes))
	}
}
