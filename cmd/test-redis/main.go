package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go-api-solve/internal/config"

	"github.com/redis/go-redis/v9"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()

	fmt.Printf("Testing Redis connection with config:\n")
	fmt.Printf("Redis Host: %s\n", cfg.GetRedisAddr())
	fmt.Printf("Redis Password: %s\n", maskPassword(cfg.Redis.Password))
	fmt.Printf("Redis DB: %d\n", cfg.Redis.DB)
	fmt.Println(strings.Repeat("=", 50))

	// 测试Redis连接
	testRedisConnection(cfg)
}

func testRedisConnection(cfg *config.Config) {
	// 创建Redis客户端
	client := redis.NewClient(&redis.Options{
		Addr:         cfg.GetRedisAddr(),
		Username:     cfg.Redis.Username,
		Password:     cfg.Redis.Password,
		DB:           cfg.Redis.DB,
		DialTimeout:  10 * time.Second,
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 5 * time.Second,
	})
	defer client.Close()

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	fmt.Printf("Step 1: Connecting to Redis...\n")
	result, err := client.Ping(ctx).Result()
	if err != nil {
		fmt.Printf("❌ Redis PING failed: %v\n", err)
		
		// 尝试不同的连接参数
		fmt.Println("\nTrying alternative connection settings...")
		testAlternativeConnection(cfg)
		return
	}
	fmt.Printf("✅ Redis PING successful: %s\n", result)

	// 测试基本操作
	fmt.Printf("\nStep 2: Testing basic operations...\n")
	testKey := "test:connection:" + fmt.Sprintf("%d", time.Now().Unix())
	testValue := "test_value_" + fmt.Sprintf("%d", time.Now().Unix())

	// 设置值
	err = client.Set(ctx, testKey, testValue, time.Minute).Err()
	if err != nil {
		fmt.Printf("❌ Redis SET failed: %v\n", err)
		return
	}
	fmt.Printf("✅ Redis SET successful\n")

	// 获取值
	val, err := client.Get(ctx, testKey).Result()
	if err != nil {
		fmt.Printf("❌ Redis GET failed: %v\n", err)
		return
	}
	fmt.Printf("✅ Redis GET successful: %s\n", val)

	// 验证值
	if val == testValue {
		fmt.Printf("✅ Value verification successful\n")
	} else {
		fmt.Printf("❌ Value verification failed: expected %s, got %s\n", testValue, val)
	}

	// 删除测试键
	err = client.Del(ctx, testKey).Err()
	if err != nil {
		fmt.Printf("❌ Redis DEL failed: %v\n", err)
	} else {
		fmt.Printf("✅ Redis DEL successful\n")
	}

	fmt.Println("\n🎉 Redis connection test completed successfully!")
}

func testAlternativeConnection(cfg *config.Config) {
	// 尝试不使用用户名
	fmt.Println("Trying without username...")
	client := redis.NewClient(&redis.Options{
		Addr:         cfg.GetRedisAddr(),
		Password:     cfg.Redis.Password,
		DB:           cfg.Redis.DB,
		DialTimeout:  10 * time.Second,
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 5 * time.Second,
	})
	defer client.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := client.Ping(ctx).Result()
	if err != nil {
		fmt.Printf("❌ Alternative connection failed: %v\n", err)
		
		// 尝试不使用密码
		fmt.Println("Trying without password...")
		client2 := redis.NewClient(&redis.Options{
			Addr:         cfg.GetRedisAddr(),
			DB:           cfg.Redis.DB,
			DialTimeout:  10 * time.Second,
			ReadTimeout:  5 * time.Second,
			WriteTimeout: 5 * time.Second,
		})
		defer client2.Close()

		result2, err2 := client2.Ping(ctx).Result()
		if err2 != nil {
			fmt.Printf("❌ No-password connection failed: %v\n", err2)
		} else {
			fmt.Printf("✅ No-password connection successful: %s\n", result2)
		}
		return
	}
	fmt.Printf("✅ Alternative connection successful: %s\n", result)
}

func maskPassword(password string) string {
	if len(password) <= 4 {
		return "****"
	}
	return password[:2] + "****" + password[len(password)-2:]
}
