package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go-api-solve/internal/config"
)

func main() {
	cfg := config.LoadConfig()
	
	fmt.Println("Testing Qwen VL Plus with correct OpenAI format...")
	
	// 使用官方文档中的正确格式
	requestBody := map[string]interface{}{
		"model": "qwen-vl-plus",
		"messages": []map[string]interface{}{
			{
				"role": "user",
				"content": []map[string]interface{}{
					{
						"type": "text",
						"text": "请识别这张图片中的题目内容，并以JSON格式返回题目信息",
					},
					{
						"type": "image_url",
						"image_url": map[string]string{
							"url": "http://solve.igmdns.com/img/24.jpg",
						},
					},
				},
			},
		},
		"temperature": 0.0,
		"top_p":       0.8,
	}
	
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("❌ Marshal error: %v\n", err)
		return
	}
	
	fmt.Printf("Request body: %s\n", string(jsonData))
	
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	
	req, err := http.NewRequestWithContext(ctx, "POST", 
		"https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", 
		bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Request creation error: %v\n", err)
		return
	}
	
	req.Header.Set("Authorization", "Bearer "+cfg.AI.QwenKey)
	req.Header.Set("Content-Type", "application/json")
	
	fmt.Println("Sending request...")
	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Request error: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Read response error: %v\n", err)
		return
	}
	
	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(bodyBytes))
	
	if resp.StatusCode == 200 {
		fmt.Println("✅ Success!")
		
		// 解析响应
		var response map[string]interface{}
		if err := json.Unmarshal(bodyBytes, &response); err == nil {
			if choices, ok := response["choices"].([]interface{}); ok && len(choices) > 0 {
				if choice, ok := choices[0].(map[string]interface{}); ok {
					if message, ok := choice["message"].(map[string]interface{}); ok {
						if content, ok := message["content"].(string); ok {
							fmt.Printf("\n📝 AI Response Content:\n%s\n", content)
						}
					}
				}
			}
		}
	} else {
		fmt.Println("❌ Failed!")
	}
}
