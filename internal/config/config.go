package config

import (
	"fmt"
	"os"
)

// Config 应用配置结构
type Config struct {
	Server   ServerConfig   `json:"server"`
	Database DatabaseConfig `json:"database"`
	Redis    RedisConfig    `json:"redis"`
	AI       AIConfig       `json:"ai"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `json:"port"`
	Mode string `json:"mode"` // debug, release, test
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	Database string `json:"database"`
	Charset  string `json:"charset"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	DB       int    `json:"db"`
}

// AIConfig AI模型配置
type AIConfig struct {
	QwenKey     string `json:"qwen_key"`
	DeepseekKey string `json:"deepseek_key"`
}

// LoadConfig 加载配置
func LoadConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("MYSQL_HOST", "***********"),
			Port:     getEnv("MYSQL_PORT", "3380"),
			Username: getEnv("MYSQL_USERNAME", "gmdns"),
			Password: getEnv("MYSQL_PASSWORD", "5e7fFn3HpPfuQ6Qx42Az"),
			Database: getEnv("MYSQL_DATABASE", "solve_api_go"),
			Charset:  getEnv("MYSQL_CHARSET", "utf8mb4"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", "EtdDj8xJ385pUPUT"),
			DB:       0,
		},
		AI: AIConfig{
			QwenKey:     getEnv("QWEN_KEY", "sk-3920274bedf642c2b7495f534aadca84"),
			DeepseekKey: getEnv("DEEPSEEK_KEY", "***********************************"),
		},
	}
}

// GetDSN 获取数据库连接字符串
func (c *Config) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		c.Database.Username,
		c.Database.Password,
		c.Database.Host,
		c.Database.Port,
		c.Database.Database,
		c.Database.Charset,
	)
}

// GetRedisAddr 获取Redis连接地址
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", c.Redis.Host, c.Redis.Port)
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
