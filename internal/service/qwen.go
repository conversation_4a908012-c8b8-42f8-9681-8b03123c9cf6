package service

import (
	"context"
	"encoding/json"
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/repository"
	"go-api-solve/internal/utils"
	"go-api-solve/pkg/ai"
)

// QwenService Qwen服务
type QwenService struct {
	qwenClient           *ai.QwenClient
	modelConfigRepo      *repository.ModelConfigRepository
}

// NewQwenService 创建新的Qwen服务
func NewQwenService(qwenClient *ai.QwenClient, modelConfigRepo *repository.ModelConfigRepository) *QwenService {
	return &QwenService{
		qwenClient:      qwenClient,
		modelConfigRepo: modelConfigRepo,
	}
}

// ProcessImage 处理图片，调用Qwen模型进行识别
func (s *QwenService) ProcessImage(ctx context.Context, imageURL string) (*model.QwenData, string, error) {
	// 获取Qwen模型配置
	config, err := s.modelConfigRepo.GetByModelName("qwen-vl-plus")
	if err != nil {
		return nil, "", fmt.Errorf("failed to get qwen model config: %w", err)
	}

	// 调用Qwen API
	response, err := s.qwenClient.CallQwenVLPlus(ctx, imageURL, config)
	if err != nil {
		return nil, "", fmt.Errorf("failed to call qwen API: %w", err)
	}

	// 提取响应内容
	content, err := ai.ExtractContentFromResponse(response)
	if err != nil {
		return nil, "", fmt.Errorf("failed to extract content from qwen response: %w", err)
	}

	// 格式化Qwen数据
	qwenData, err := utils.FormatQwenData(content)
	if err != nil {
		return nil, "", fmt.Errorf("failed to format qwen data: %w", err)
	}

	// 返回格式化的数据和原始响应
	rawResponse, _ := json.Marshal(response)
	return qwenData, string(rawResponse), nil
}
